from typing import Annotated
from litestar import Controller, post, get
from litestar.di import Provide
from litestar.params import Body, Parameter
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.config import Settings
from app.DTOs.api_models import (
    SummarizationTaskRequest,
    BaseTaskResponse,
    SummarizationTaskStatusResponse,
    TaskStatusEnum,
    SummaryData,
)
from app.DTOs.task_models import SummarizationTaskDataInternal
from app.services.cache import CacheService
from app.tasks.manager import TaskQueueManager
from app.utils.general_utils import generate_summary_task_id
from app.exceptions import InvalidSummarizationModeException


class SummaryController(Controller):
    path = "/summaries"
    dependencies = {
        "cache_service": Provide(CacheService, sync_to_thread=False),
        "task_queue_manager": Provide(TaskQueueManager, sync_to_thread=False),
        "settings": Provide(Settings, sync_to_thread=False),
    }

    @post(path="/tasks", summary="Submit a new text summarization task.")
    async def submit_summary_task(
        self,
        data: Annotated[SummarizationTaskRequest, Body()],
        db_session: AsyncSession,
        cache_service: CacheService,
        task_queue_manager: TaskQueueManager,
        settings: Settings,
    ) -> BaseTaskResponse:
        logger.info(f"Received summary task request for mode: {data.mode}")

        if data.mode not in settings.SUMMARIZATION_MODES:
            raise InvalidSummarizationModeException(
                f"Summarization mode '{data.mode}' is not configured."
            )

        task_id = generate_summary_task_id(data.text, data.mode)

        # 1. Check cache
        cached_data = await cache_service.get_cached_summary(task_id, db_session)
        if cached_data:
            await cache_service.upsert_task_log_status(
                task_id=task_id,
                task_type="summary",
                status=TaskStatusEnum.CACHED,
                result_reference_id=task_id,
                session=db_session,
            )
            await task_queue_manager.notification_service.broadcast_update(
                task_id=task_id,
                task_type="summary",
                status=TaskStatusEnum.CACHED,
                data=cached_data,
            )
            logger.info(f"Task {task_id} (summary) found in cache.")
            return BaseTaskResponse(
                task_id=task_id,
                status=TaskStatusEnum.CACHED,
                detail="Result retrieved from cache.",
            )

        # 2. Submit to queue
        task_internal_data = SummarizationTaskDataInternal(
            task_id=task_id, text_to_summarize=data.text, mode=data.mode
        )

        task_id_resp, status, error_detail = await task_queue_manager.submit_task(
            task_internal_data, cache_service
        )

        return BaseTaskResponse(
            task_id=task_id_resp, status=status, detail=error_detail
        )

    @get(
        path="/tasks/{task_id_hash:str}",
        summary="Get status and result of a summarization task.",
    )
    async def get_summary_task_status(
        self,
        task_id_hash: Annotated[
            str, Parameter(description="The hash ID of the summarization task.")
        ],
        db_session: AsyncSession,
        task_queue_manager: TaskQueueManager,
        cache_service: CacheService,
    ) -> SummarizationTaskStatusResponse:
        logger.info(f"Fetching status for summary task: {task_id_hash}")

        in_memory_status = await task_queue_manager.get_task_status(task_id_hash)
        if in_memory_status:
            return SummarizationTaskStatusResponse(
                task_id=task_id_hash,
                status=in_memory_status.status,
                data=SummaryData.model_validate(in_memory_status.data)
                if in_memory_status.data
                and in_memory_status.status == TaskStatusEnum.COMPLETED
                else None,
                error=in_memory_status.error,
            )

        cached_result = await cache_service.get_cached_summary(task_id_hash, db_session)
        if cached_result:
            return SummarizationTaskStatusResponse(
                task_id=task_id_hash,
                status=TaskStatusEnum.COMPLETED,
                data=cached_result,
            )

        task_log_entry = await cache_service.get_task_log_entry(
            task_id_hash, db_session
        )
        if task_log_entry:
            return SummarizationTaskStatusResponse(
                task_id=task_id_hash,
                status=task_log_entry.status,  # type: ignore
                error=task_log_entry.error_message,
            )

        return SummarizationTaskStatusResponse(
            task_id=task_id_hash,
            status=TaskStatusEnum.FAILED,
            error="Task not found or status unknown.",
        )
