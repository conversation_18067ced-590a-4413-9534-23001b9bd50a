from typing import Annotated
from litestar import Controller, websocket
from litestar.di import Provide
from litestar.params import Parameter
from litestar.websockets import WebSocket
from litestar.exceptions import WebSocketDisconnect
from loguru import logger
from sqlalchemy.ext.asyncio import (
    AsyncSession,
)  # Needed if we fetch initial status from DB


from app.services.notification import NotificationService
from app.tasks.manager import TaskQueueManager
from app.services.cache import (
    CacheService,
)  # To fetch initial status for already completed tasks
from app.DTOs.api_models import (
    TaskStatusEnum,
    SubtitleData,
    SummaryData,
    PlaylistData,
)  # For casting


class WebSocketTaskController(Controller):
    dependencies = {
        "notification_service": Provide(NotificationService, sync_to_thread=False),
        "task_queue_manager": Provide(TaskQueueManager, sync_to_thread=False),
        "cache_service": Provide(
            CacheService, sync_to_thread=False
        ),  # For initial status
        "db_session": Provide(AsyncSession, sync_to_thread=False),  # For cache_service
    }

    @websocket(
        path="/tasks/{task_id:str}",
        summary="Subscribe to real-time updates for a task.",
    )
    async def task_status_websocket_handler(
        self,
        socket: WebSocket,
        task_id: Annotated[
            str, Parameter(description="The ID of the task to monitor.")
        ],
        notification_service: NotificationService,
        task_queue_manager: TaskQueueManager,
        cache_service: CacheService,
        db_session: AsyncSession,  # Injected by Litestar if needed
    ) -> None:
        await notification_service.connect(socket, task_id)
        logger.info(
            f"WebSocket client connected for task_id: {task_id} from {socket.client}"
        )

        # Send current status immediately upon connection
        initial_status_sent = False
        current_status = await task_queue_manager.get_task_status(task_id)
        if current_status:
            await socket.send_json(current_status.model_dump())
            initial_status_sent = True
            logger.debug(
                f"Sent initial in-memory status {current_status.status} for {task_id} to WebSocket."
            )
        else:
            # If not in memory, check DB (TaskStatusLog and then specific caches)
            task_log = await cache_service.get_task_log_entry(task_id, db_session)
            if task_log:
                data_to_send = None
                if task_log.status == TaskStatusEnum.COMPLETED:
                    if (
                        task_log.task_type == "subtitle"
                        and task_log.result_reference_id
                    ):
                        data_to_send = await cache_service.get_cached_subtitles(
                            task_log.result_reference_id, db_session
                        )
                    elif (
                        task_log.task_type == "summary" and task_log.result_reference_id
                    ):
                        data_to_send = await cache_service.get_cached_summary(
                            task_log.result_reference_id, db_session
                        )
                    elif (
                        task_log.task_type == "playlist"
                        and task_log.result_reference_id
                    ):
                        data_to_send = await cache_service.get_cached_playlist(
                            task_log.result_reference_id, db_session
                        )

                ws_update_from_log = {
                    "task_id": task_id,
                    "task_type": task_log.task_type,
                    "status": task_log.status.value,  # Ensure enum value is sent
                    "data": data_to_send.model_dump() if data_to_send else None,
                    "error": task_log.error_message,
                    "progress": None,  # Progress not stored in TaskStatusLog in this design
                }
                await socket.send_json(ws_update_from_log)
                initial_status_sent = True
                logger.debug(
                    f"Sent initial DB log status {task_log.status} for {task_id} to WebSocket."
                )

        if not initial_status_sent:
            # If no status anywhere, send a PENDING or NOT_FOUND perhaps
            await socket.send_json(
                {
                    "task_id": task_id,
                    "status": TaskStatusEnum.PENDING.value,
                    "task_type": "unknown",  # or fail
                    "detail": "Task status being retrieved or task not found.",
                }
            )
            logger.debug(
                f"Sent default PENDING status for {task_id} to WebSocket as no initial found."
            )

        try:
            while True:
                # Keep the connection alive, Litestar handles ping/pong if configured.
                # We are primarily a publisher here. Litestar's WebSocket handler will
                # keep it alive. If client sends data, it would be received here.
                await (
                    socket.receive_text()
                )  # Or receive_json if expecting client messages
                # For this use case, we mostly broadcast, so client messages might not be expected.
                # A simple receive can detect disconnects.
        except WebSocketDisconnect:
            logger.info(f"WebSocket client for task_id: {task_id} disconnected.")
        except Exception as e:
            logger.error(f"Error in WebSocket handler for task {task_id}: {e}")
        finally:
            await notification_service.disconnect(socket, task_id)
            logger.info(f"Cleaned up WebSocket connection for task_id: {task_id}")
