from typing import Annotated
from litestar import Controller, post, get
from litestar.di import Provide
from litestar.params import Body, Parameter
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.config import Settings
from app.DTOs.api_models import (
    SubtitleTaskRequest,
    BaseTaskResponse,
    SubtitleTaskStatusResponse,
    TaskStatusEnum,
    SubtitleData,
)
from app.DTOs.task_models import SubtitleTaskDataInternal
from app.services.youtube import YouTubeService
from app.services.cache import CacheService
from app.tasks.manager import TaskQueueManager
from app.utils.general_utils import extract_video_id
from app.exceptions import InvalidYouTubeURLException


class SubtitleController(Controller):
    path = "/subtitles"
    dependencies = {
        "youtube_service": Provide(YouTubeService, sync_to_thread=False),
        "cache_service": Provide(CacheService, sync_to_thread=False),
        "task_queue_manager": Provide(TaskQueueManager, sync_to_thread=False),
        "settings": Provide(Settings, sync_to_thread=False),
    }

    @post(path="/tasks", summary="Submit a new subtitle extraction task.")
    async def submit_subtitle_task(
        self,
        data: Annotated[SubtitleTaskRequest, Body()],
        db_session: AsyncSession,  # Injected by Litestar via get_db_session
        youtube_service: YouTubeService,  # Injected from controller dependencies
        cache_service: CacheService,
        task_queue_manager: TaskQueueManager,
    ) -> BaseTaskResponse:
        logger.info(f"Received subtitle task request for URL: {data.youtube_url}")

        video_id = extract_video_id(str(data.youtube_url))
        if not video_id:
            logger.warning(f"Invalid YouTube URL received: {data.youtube_url}")
            raise InvalidYouTubeURLException()

        # 1. Check cache
        cached_data = await cache_service.get_cached_subtitles(video_id, db_session)
        if cached_data:
            # Ensure TaskStatusLog also reflects this if it's a fresh request for cached data
            await cache_service.upsert_task_log_status(
                task_id=video_id,
                task_type="subtitle",
                status=TaskStatusEnum.CACHED,
                result_reference_id=video_id,
                session=db_session,
            )
            # Notify via WebSocket if someone is listening for this "new" cached task
            await task_queue_manager.notification_service.broadcast_update(
                task_id=video_id,
                task_type="subtitle",
                status=TaskStatusEnum.CACHED,
                data=cached_data,
            )
            logger.info(
                f"Task for video_id {video_id} found in cache. Returning cached data."
            )
            return BaseTaskResponse(
                task_id=video_id,
                status=TaskStatusEnum.CACHED,
                detail="Result retrieved from cache.",
            )

        # 2. If not cached, submit to queue
        task_internal_data = SubtitleTaskDataInternal(
            task_id=video_id, video_id=video_id, youtube_url=data.youtube_url
        )

        task_id, status, error_detail = await task_queue_manager.submit_task(
            task_internal_data, cache_service
        )  # Pass cache_service for DB log

        return BaseTaskResponse(task_id=task_id, status=status, detail=error_detail)

    @get(
        path="/tasks/{video_id:str}",
        summary="Get status and result of a subtitle task.",
    )
    async def get_subtitle_task_status(
        self,
        video_id: Annotated[str, Parameter(description="The YouTube video ID.")],
        db_session: AsyncSession,
        task_queue_manager: TaskQueueManager,
        cache_service: CacheService,
    ) -> SubtitleTaskStatusResponse:
        logger.info(f"Fetching status for subtitle task: {video_id}")

        # 1. Check in-memory status (quickest)
        in_memory_status = await task_queue_manager.get_task_status(video_id)
        if in_memory_status:
            logger.debug(
                f"Status for {video_id} found in-memory: {in_memory_status.status}"
            )
            return SubtitleTaskStatusResponse(
                task_id=video_id,
                status=in_memory_status.status,
                data=SubtitleData.model_validate(in_memory_status.data)
                if in_memory_status.data
                and in_memory_status.status == TaskStatusEnum.COMPLETED
                else None,
                error=in_memory_status.error,
            )

        # 2. Check DB cache for completed result (SubtitlesCache)
        cached_result = await cache_service.get_cached_subtitles(video_id, db_session)
        if cached_result:
            logger.debug(
                f"Result for {video_id} found in SubtitlesCache (implies COMPLETED)."
            )
            return SubtitleTaskStatusResponse(
                task_id=video_id, status=TaskStatusEnum.COMPLETED, data=cached_result
            )

        # 3. Check persistent TaskStatusLog from DB
        task_log_entry = await cache_service.get_task_log_entry(video_id, db_session)
        if task_log_entry:
            logger.debug(
                f"Status for {video_id} found in TaskStatusLog: {task_log_entry.status}"
            )
            # If completed, data should be in SubtitlesCache, but if somehow not, this indicates an issue.
            # For now, assume if COMPLETED in log, data is in cache (handled by step 2).
            return SubtitleTaskStatusResponse(
                task_id=video_id,
                status=task_log_entry.status,  # type: ignore
                error=task_log_entry.error_message,
                # Data is not directly in TaskStatusLog, so if status is COMPLETED here but not in cache, it's an anomaly.
            )

        logger.warning(
            f"No status found for subtitle task: {video_id}. Assuming not submitted or an issue."
        )
        return SubtitleTaskStatusResponse(
            task_id=video_id,
            status=TaskStatusEnum.FAILED,
            error="Task not found or status unknown.",
        )
