from litestar.status_codes import (
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_429_TOO_MANY_REQUESTS,
    HTTP_500_INTERNAL_SERVER_ERROR,
    HTTP_503_SERVICE_UNAVAILABLE,
)


class AppException(Exception):
    """Base application exception."""

    status_code: int = HTTP_500_INTERNAL_SERVER_ERROR
    detail: str = "An unexpected error occurred."

    def __init__(self, detail: str | None = None, status_code: int | None = None):
        self.detail = detail or self.detail
        self.status_code = status_code or self.status_code
        super().__init__(self.detail)


class InvalidYouTubeURLException(AppException):
    status_code = HTTP_400_BAD_REQUEST
    detail = "Invalid YouTube URL provided."


class VideoNotFoundException(AppException):
    status_code = HTTP_404_NOT_FOUND
    detail = "YouTube video not found or access is restricted."


class SubtitlesNotAvailableException(AppException):
    status_code = HTTP_404_NOT_FOUND
    detail = "No suitable subtitles (Russian or English) found for this video."


class PlaylistProcessingFailedException(AppException):
    status_code = HTTP_500_INTERNAL_SERVER_ERROR
    detail = "Failed to process the YouTube playlist or channel."


class SummarizationFailedException(AppException):
    status_code = HTTP_500_INTERNAL_SERVER_ERROR
    detail = "AI summarization failed."


class GeminiAPIException(AppException):
    status_code = HTTP_503_SERVICE_UNAVAILABLE  # Or specific code from Gemini
    detail = "Error communicating with Gemini API."


class TaskQueueFullException(AppException):
    status_code = HTTP_503_SERVICE_UNAVAILABLE
    detail = "Task queue is full. Please try again later."


class CacheMissException(AppException):
    status_code = HTTP_404_NOT_FOUND  # Or handled internally without HTTP error
    detail = "Item not found in cache."


class InvalidSummarizationModeException(AppException):
    status_code = HTTP_400_BAD_REQUEST
    detail = "The specified summarization mode is not configured."
