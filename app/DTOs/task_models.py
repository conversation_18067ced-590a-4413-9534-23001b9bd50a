from typing import Union
from pydantic import BaseModel, HttpUrl

# These models are for internal use within the task queue and workers


class BaseTaskData(BaseModel):
    task_id: str
    task_type: str  # Literal['subtitle', 'summary', 'playlist'] can be used


class SubtitleTaskDataInternal(BaseTaskData):
    task_type: str = "subtitle"
    youtube_url: HttpUrl  # Keep original URL for yt-dlp
    video_id: str  # Already extracted, used as task_id


class SummarizationTaskDataInternal(BaseTaskData):
    task_type: str = "summary"
    text_to_summarize: str
    mode: str
    # task_id is hash(text+mode)


class PlaylistTaskDataInternal(BaseTaskData):
    task_type: str = "playlist"
    playlist_url: HttpUrl  # Original URL
    # task_id is hash(playlist_url)


TaskDataUnion = Union[
    SubtitleTaskDataInternal,
    SummarizationTaskDataInternal,
    PlaylistTaskDataInternal,
]
