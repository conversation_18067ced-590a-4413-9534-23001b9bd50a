from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, HttpUrl, field_validator, Field


class TaskStatusEnum(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CACHED = "cached"


class BaseTaskResponse(BaseModel):
    task_id: str
    status: TaskStatusEnum
    detail: Optional[str] = None


# --- Subtitle Task ---
class SubtitleTaskRequest(BaseModel):
    youtube_url: HttpUrl


class SubtitleData(BaseModel):
    video_id: str
    title: Optional[str] = None
    publish_date: Optional[str] = None  # ISO format string
    original_language: Optional[str] = None
    subtitles_ru: Optional[str] = None
    subtitles_en: Optional[str] = None


class SubtitleTaskStatusResponse(BaseTaskResponse):
    data: Optional[SubtitleData] = None
    error: Optional[str] = None


# --- Summarization Task ---
class SummarizationTaskRequest(BaseModel):
    text: str = Field(..., min_length=10)  # Минимальная длина текста для суммаризации
    mode: str = "default"

    @field_validator("mode")
    @classmethod
    def mode_must_be_configured(cls, v: str) -> str:
        # Динамическая валидация по ключам из settings.SUMMARIZATION_MODES
        # Это потребует доступа к settings, что лучше делать в сервисе или контроллере
        # Здесь можно оставить простую проверку или убрать, если валидация будет глубже.
        # from app.config import settings # Осторожно с циклическими импортами
        # if v not in settings.SUMMARIZATION_MODES:
        #     raise ValueError(f"Unknown summarization mode: {v}")
        return v


class SummaryData(BaseModel):
    summary: str


class SummarizationTaskStatusResponse(BaseTaskResponse):
    data: Optional[SummaryData] = None
    error: Optional[str] = None


# --- Playlist Task ---
class PlaylistTaskRequest(BaseModel):
    playlist_url: HttpUrl  # Может быть URL канала или плейлиста


class PlaylistData(BaseModel):
    video_ids: List[str]


class PlaylistTaskStatusResponse(BaseTaskResponse):
    data: Optional[PlaylistData] = None
    error: Optional[str] = None


# --- WebSocket Message ---
class WebSocketUpdate(BaseModel):
    task_id: str
    status: TaskStatusEnum
    task_type: str  # 'subtitle', 'summary', 'playlist'
    progress: Optional[float] = None
    data: Optional[Any] = None  # SubtitleData, SummaryData, or PlaylistData
    error: Optional[str] = None
