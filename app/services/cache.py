from datetime import datetime
import json
from typing import Optional, List, Any
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from sqlalchemy import select, update, delete
from sqlalchemy.dialects.postgresql import (
    insert as pg_insert,
)  # For ON CONFLICT DO UPDATE
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger
import hashlib

from app.database.models import (
    SubtitlesCache,
    SummarizationCache,
    PlaylistCache,
    TaskStatusLog,
)
from app.DTOs.api_models import (
    SubtitleData,
    SummaryData,
    PlaylistData,
    TaskStatusEnum,
    WebSocketUpdate,
)
from app.utils.general_utils import (
    generate_summary_task_id,
    generate_playlist_task_id,
)  # For original_text_hash, playlist_url_hash


class CacheService:
    def __init__(
        self, db_session_factory: Optional[async_sessionmaker[AsyncSession]] = None
    ):
        # db_session_factory is for workers, controllers get session via DI
        self._db_session_factory = db_session_factory

    async def _get_session(
        self, provided_session: Optional[AsyncSession] = None
    ) -> AsyncSession:
        if provided_session:
            return provided_session
        if self._db_session_factory:
            return self._db_session_factory()
        raise RuntimeError("Database session/factory not provided to CacheService")

    # --- TaskStatusLog Methods ---
    async def get_task_log_entry(
        self, task_id: str, session: Optional[AsyncSession] = None
    ) -> Optional[TaskStatusLog]:
        db_session = await self._get_session(session)
        try:
            stmt = select(TaskStatusLog).where(TaskStatusLog.task_id == task_id)
            result = await db_session.execute(stmt)
            entry = result.scalar_one_or_none()
            return entry
        except SQLAlchemyError as e:
            logger.error(f"Error getting task log for {task_id}: {e}")
            return None
        finally:
            if (
                not session and self._db_session_factory
            ):  # if session was created internally
                await db_session.close()

    async def upsert_task_log_status(
        self,
        task_id: str,
        task_type: str,
        status: TaskStatusEnum,
        result_reference_id: Optional[str] = None,
        error_message: Optional[str] = None,
        session: Optional[AsyncSession] = None,
    ):
        db_session = await self._get_session(session)
        try:
            # For PostgreSQL: ON CONFLICT DO UPDATE
            if db_session.bind and db_session.bind.dialect.name == "postgresql":
                stmt = pg_insert(TaskStatusLog).values(
                    task_id=task_id,
                    task_type=task_type,
                    status=status,
                    result_reference_id=result_reference_id,
                    error_message=error_message,
                    updated_at=datetime.utcnow(),
                )
                stmt = stmt.on_conflict_do_update(
                    index_elements=[
                        TaskStatusLog.task_id
                    ],  # Ensure task_id is unique or has a unique constraint
                    set_={
                        "status": status,
                        "task_type": task_type,  # Should not change, but good to include
                        "result_reference_id": result_reference_id,
                        "error_message": error_message,
                        "updated_at": datetime.utcnow(),
                    },
                )
            else:  # For SQLite and other DBs, perform select then insert/update
                existing_log = await db_session.get(
                    TaskStatusLog, task_id
                )  # Assuming task_id is PK
                if not existing_log:  # If TaskStatusLog has autoincrement pk 'id', and task_id is indexed but not PK
                    # Need to query by task_id first
                    result = await db_session.execute(
                        select(TaskStatusLog).filter_by(task_id=task_id)
                    )
                    existing_log = result.scalar_one_or_none()

                if existing_log:
                    existing_log.status = status
                    existing_log.result_reference_id = result_reference_id
                    existing_log.error_message = error_message
                    existing_log.updated_at = datetime.utcnow()
                    db_session.add(existing_log)
                    stmt = None  # No separate statement needed
                else:
                    new_log = TaskStatusLog(
                        task_id=task_id,
                        task_type=task_type,
                        status=status,
                        result_reference_id=result_reference_id,
                        error_message=error_message,
                    )
                    db_session.add(new_log)
                    stmt = None  # No separate statement needed

            if stmt is not None:  # Only for PostgreSQL insert statement
                await db_session.execute(stmt)

            await db_session.commit()
            logger.debug(f"Task log for {task_id} upserted to status {status}.")
        except SQLAlchemyError as e:
            await db_session.rollback()
            logger.error(f"Error upserting task log for {task_id}: {e}")
            raise  # Re-raise to be handled by caller
        finally:
            if not session and self._db_session_factory:
                await db_session.close()

    # --- Subtitles Cache ---
    async def get_cached_subtitles(
        self, video_id: str, session: AsyncSession
    ) -> Optional[SubtitleData]:
        try:
            result = await session.get(SubtitlesCache, video_id)
            if result:
                logger.info(f"Subtitles cache hit for video_id: {video_id}")
                return SubtitleData(
                    video_id=result.video_id,
                    title=result.title,
                    publish_date=result.publish_date.isoformat()
                    if result.publish_date
                    else None,
                    original_language=result.original_language,
                    subtitles_ru=result.subtitles_ru,
                    subtitles_en=result.subtitles_en,
                )
            logger.info(f"Subtitles cache miss for video_id: {video_id}")
            return None
        except SQLAlchemyError as e:
            logger.error(f"Error getting cached subtitles for {video_id}: {e}")
            return None  # Or re-raise

    async def store_subtitles(self, data: SubtitleData, session: AsyncSession):
        try:
            # Convert publish_date string to datetime object if not None
            publish_date_obj = (
                datetime.fromisoformat(data.publish_date.replace("Z", "+00:00"))
                if data.publish_date
                else None
            )

            cached_item = SubtitlesCache(
                video_id=data.video_id,
                title=data.title,
                publish_date=publish_date_obj,
                original_language=data.original_language,
                subtitles_ru=data.subtitles_ru,
                subtitles_en=data.subtitles_en,
                processed_at=datetime.utcnow(),
            )
            # Upsert logic (example for PostgreSQL)
            if session.bind and session.bind.dialect.name == "postgresql":
                stmt = (
                    pg_insert(SubtitlesCache)
                    .values(
                        video_id=data.video_id,
                        title=data.title,
                        publish_date=publish_date_obj,
                        original_language=data.original_language,
                        subtitles_ru=data.subtitles_ru,
                        subtitles_en=data.subtitles_en,
                        processed_at=datetime.utcnow(),
                    )
                    .on_conflict_do_update(
                        index_elements=["video_id"],
                        set_={
                            "title": data.title,
                            "publish_date": publish_date_obj,
                            "original_language": data.original_language,
                            "subtitles_ru": data.subtitles_ru,
                            "subtitles_en": data.subtitles_en,
                            "processed_at": datetime.utcnow(),
                        },
                    )
                )
                await session.execute(stmt)
            else:  # SQLite compatible merge
                await session.merge(cached_item)

            await session.commit()
            logger.info(f"Stored subtitles for video_id: {data.video_id}")
        except SQLAlchemyError as e:
            await session.rollback()
            logger.error(f"Error storing subtitles for {data.video_id}: {e}")
            raise

    # --- Summarization Cache ---
    async def get_cached_summary(
        self, task_id: str, session: AsyncSession
    ) -> Optional[SummaryData]:
        try:
            result = await session.get(SummarizationCache, task_id)
            if result:
                logger.info(f"Summary cache hit for task_id: {task_id}")
                return SummaryData(summary=result.summary_text)
            logger.info(f"Summary cache miss for task_id: {task_id}")
            return None
        except SQLAlchemyError as e:
            logger.error(f"Error getting cached summary for {task_id}: {e}")
            return None

    async def store_summary(
        self,
        task_id: str,
        original_text: str,
        mode: str,
        summary_text: str,
        session: AsyncSession,
    ):
        try:
            original_text_hash = hashlib.sha256(
                original_text.encode("utf-8")
            ).hexdigest()
            cached_item = SummarizationCache(
                task_id=task_id,
                original_text_hash=original_text_hash,
                mode=mode,
                summary_text=summary_text,
                processed_at=datetime.utcnow(),
            )
            if session.bind and session.bind.dialect.name == "postgresql":
                stmt = (
                    pg_insert(SummarizationCache)
                    .values(
                        task_id=task_id,
                        original_text_hash=original_text_hash,
                        mode=mode,
                        summary_text=summary_text,
                        processed_at=datetime.utcnow(),
                    )
                    .on_conflict_do_update(
                        index_elements=["task_id"],
                        set_={
                            "original_text_hash": original_text_hash,
                            "mode": mode,
                            "summary_text": summary_text,
                            "processed_at": datetime.utcnow(),
                        },
                    )
                )
                await session.execute(stmt)
            else:
                await session.merge(cached_item)

            await session.commit()
            logger.info(f"Stored summary for task_id: {task_id}")
        except SQLAlchemyError as e:
            await session.rollback()
            logger.error(f"Error storing summary for {task_id}: {e}")
            raise

    # --- Playlist Cache ---
    async def get_cached_playlist(
        self, task_id: str, session: AsyncSession
    ) -> Optional[PlaylistData]:
        try:
            result = await session.get(PlaylistCache, task_id)
            if result:
                logger.info(f"Playlist cache hit for task_id: {task_id}")
                video_ids = json.loads(result.video_ids_json)
                return PlaylistData(video_ids=video_ids)
            logger.info(f"Playlist cache miss for task_id: {task_id}")
            return None
        except (SQLAlchemyError, json.JSONDecodeError) as e:
            logger.error(f"Error getting cached playlist for {task_id}: {e}")
            return None

    async def store_playlist(
        self,
        task_id: str,
        playlist_url: str,
        video_ids: List[str],
        session: AsyncSession,
    ):
        try:
            playlist_url_hash = hashlib.sha256(
                str(playlist_url).encode("utf-8")
            ).hexdigest()
            video_ids_json = json.dumps(video_ids)
            cached_item = PlaylistCache(
                task_id=task_id,
                playlist_url_hash=playlist_url_hash,
                video_ids_json=video_ids_json,
                processed_at=datetime.utcnow(),
            )
            if session.bind and session.bind.dialect.name == "postgresql":
                stmt = (
                    pg_insert(PlaylistCache)
                    .values(
                        task_id=task_id,
                        playlist_url_hash=playlist_url_hash,
                        video_ids_json=video_ids_json,
                        processed_at=datetime.utcnow(),
                    )
                    .on_conflict_do_update(
                        index_elements=["task_id"],
                        set_={
                            "playlist_url_hash": playlist_url_hash,
                            "video_ids_json": video_ids_json,
                            "processed_at": datetime.utcnow(),
                        },
                    )
                )
                await session.execute(stmt)
            else:
                await session.merge(cached_item)

            await session.commit()
            logger.info(f"Stored playlist for task_id: {task_id}")
        except SQLAlchemyError as e:
            await session.rollback()
            logger.error(f"Error storing playlist for {task_id}: {e}")
            raise
