import asyncio
from typing import Dict, Set
from litestar.websockets import WebSocket
from app.DTOs.api_models import WebSocketUpdate
from loguru import logger


class NotificationService:
    def __init__(self):
        self.active_connections: Dict[
            str, Set[WebSocket]
        ] = {}  # task_id -> Set[WebSocket]
        self._lock = asyncio.Lock()

    async def connect(self, websocket: WebSocket, task_id: str):
        await websocket.accept()
        async with self._lock:
            if task_id not in self.active_connections:
                self.active_connections[task_id] = set()
            self.active_connections[task_id].add(websocket)
        logger.info(f"WebSocket connected for task_id: {task_id}")

    async def disconnect(self, websocket: WebSocket, task_id: str):
        async with self._lock:
            if task_id in self.active_connections:
                self.active_connections[task_id].remove(websocket)
                if not self.active_connections[
                    task_id
                ]:  # No more listeners for this task_id
                    del self.active_connections[task_id]
        logger.info(f"WebSocket disconnected for task_id: {task_id}")

    async def broadcast_update(self, update_message: WebSocketUpdate):
        task_id = update_message.task_id
        disconnected_sockets = set()
        async with self._lock:
            if task_id in self.active_connections:
                logger.debug(
                    f"Broadcasting update for task {task_id} to {len(self.active_connections[task_id])} clients."
                )
                for websocket in self.active_connections[task_id]:
                    try:
                        await websocket.send_json(update_message.model_dump())
                    except (
                        Exception
                    ) as e:  # ConnectionClosedOK, ConnectionClosedError etc.
                        logger.warning(
                            f"Failed to send update to WebSocket for task {task_id}: {e}. Marking for removal."
                        )
                        disconnected_sockets.add(websocket)

            # Clean up disconnected sockets outside the iteration
            if disconnected_sockets and task_id in self.active_connections:
                for sock in disconnected_sockets:
                    self.active_connections[task_id].remove(sock)
                if not self.active_connections[task_id]:
                    del self.active_connections[task_id]
