import asyncio
import j<PERSON>
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any, List
from yt_dlp import YoutubeDL
from loguru import logger

from app.utils.general_utils import parse_ttml_to_text
from app.DTOs.api_models import SubtitleData


class YouTubeService:
    def __init__(self):
        # Configure yt-dlp options
        self.ydl_opts_subs_meta = {
            "writesubtitles": True,
            "subtitleslangs": ["ru", "en"],  # Prioritize Russian, then English
            "subtitlesformat": "ttml",
            "skip_download": True,  # We only want subtitles and metadata
            "extract_flat": "discard_in_playlist",  # process playlist entries individually if called on playlist
            "quiet": True,
            "noprogress": True,
            "ignoreerrors": True,  # Continue on errors for individual videos in playlist
            "logger": logger,  # Use Loguru for yt-dlp logs
            # 'verbose': True, # For debugging yt-dlp issues
        }
        self.ydl_opts_playlist = {
            "extract_flat": "in_playlist",  # Extract only video_ids from playlist
            "skip_download": True,
            "quiet": True,
            "noprogress": True,
            "ignoreerrors": True,
            "logger": logger,
        }

    async def _run_yt_dlp(
        self, url: str, opts: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Runs yt-dlp in a separate thread to avoid blocking asyncio event loop."""
        loop = asyncio.get_running_loop()
        try:
            with YoutubeDL(opts) as ydl:
                # YoutubeDL.extract_info is synchronous, run in executor
                return await loop.run_in_executor(None, ydl.extract_info, url, False)
        except Exception as e:
            logger.error(f"yt-dlp failed for URL {url} with options {opts}: {e}")
            return None

    async def get_subtitles_and_metadata(
        self, video_url: str, video_id: str
    ) -> Optional[SubtitleData]:
        logger.info(f"Fetching subtitles and metadata for {video_id} from {video_url}")
        info_dict = await self._run_yt_dlp(video_url, self.ydl_opts_subs_meta)

        if not info_dict:
            logger.warning(f"No info extracted for {video_id}")
            return None

        subtitles_ru: Optional[str] = None
        subtitles_en: Optional[str] = None
        original_language: Optional[str] = None  # Try to determine

        if "requested_subtitles" in info_dict and info_dict["requested_subtitles"]:
            req_subs = info_dict["requested_subtitles"]
            if "ru" in req_subs and req_subs["ru"].get("data"):
                subtitles_ru = parse_ttml_to_text(req_subs["ru"]["data"])
                if not original_language:
                    original_language = "ru"
            if "en" in req_subs and req_subs["en"].get("data"):
                subtitles_en = parse_ttml_to_text(req_subs["en"]["data"])
                if not original_language:
                    original_language = "en"

        if not (subtitles_ru or subtitles_en):
            logger.warning(f"No Russian or English subtitles found for {video_id}")
            # Could return None or SubtitleData with empty subs depending on desired behavior
            # For now, let's return data even if subs are missing, caller can decide

        # yt-dlp might provide language codes like 'ru-RU'. We might want to simplify to 'ru'.
        # Also, info_dict.get('language') could be the audio language, not subtitle language.
        # Heuristic: if only one subtitle language is found, assume it's original for this context.

        return SubtitleData(
            video_id=video_id,
            title=info_dict.get("title"),
            # upload_date is 'YYYYMMDD', convert to ISO
            publish_date=f"{info_dict['upload_date'][:4]}-{info_dict['upload_date'][4:6]}-{info_dict['upload_date'][6:8]}T00:00:00Z"
            if info_dict.get("upload_date")
            else None,
            original_language=original_language,
            subtitles_ru=subtitles_ru,
            subtitles_en=subtitles_en,
        )

    async def get_playlist_video_ids(self, playlist_url: str) -> Optional[List[str]]:
        logger.info(f"Fetching video IDs for playlist/channel: {playlist_url}")
        info_dict = await self._run_yt_dlp(playlist_url, self.ydl_opts_playlist)

        if not info_dict or "entries" not in info_dict:
            logger.warning(f"No entries found for playlist/channel: {playlist_url}")
            return None

        video_ids = [
            entry["id"] for entry in info_dict["entries"] if entry and "id" in entry
        ]
        logger.info(f"Found {len(video_ids)} video IDs in {playlist_url}")
        return video_ids
