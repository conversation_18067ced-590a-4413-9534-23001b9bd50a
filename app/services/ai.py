import httpx
from loguru import logger
from app.config import Settings
from app.exceptions import GeminiAPIException, InvalidSummarizationModeException
from app.exceptions import SummarizationFailedException


class AIService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.http_client = httpx.AsyncClient(
            timeout=60.0
        )  # 60-second timeout for AI requests

    async def summarize_text(self, text: str, mode: str) -> str:
        logger.info(
            f"Starting summarization for text (hash: {hash(text)}) with mode: {mode}"
        )

        if mode not in self.settings.SUMMARIZATION_MODES:
            logger.error(f"Invalid summarization mode requested: {mode}")
            raise InvalidSummarizationModeException(
                f"Summarization mode '{mode}' is not configured."
            )

        mode_config = self.settings.SUMMARIZATION_MODES[mode]

        api_key = self.settings.GEMINI_API_KEY
        # Documentation: https://ai.google.dev/docs/gemini_api_overview?hl=ru#text-generation
        # Endpoint might vary based on model generation (e.g. v1, v1beta)
        # For gemini-1.5-flash-latest, it's likely under generativeLanguage API
        # Example: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=YOUR_API_KEY

        gemini_api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{mode_config.model_name}:generateContent"

        headers = {
            "Content-Type": "application/json",
        }
        params = {"key": api_key}

        prompt = mode_config.prompt_template.format(text=text)

        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": mode_config.temperature,
                # "maxOutputTokens": 2048, # Можно добавить, если нужно
                # "topP": 1,
                # "topK": 1,
            },
        }

        try:
            response = await self.http_client.post(
                gemini_api_url, json=payload, headers=headers, params=params
            )
            response.raise_for_status()  # Raises HTTPError for 4XX/5XX status codes

            response_data = response.json()

            # Extract text from Gemini response structure
            # Structure: response_data.candidates[0].content.parts[0].text
            if (
                response_data.get("candidates")
                and len(response_data["candidates"]) > 0
                and response_data["candidates"][0].get("content")
                and response_data["candidates"][0]["content"].get("parts")
                and len(response_data["candidates"][0]["content"]["parts"]) > 0
            ):
                summary = response_data["candidates"][0]["content"]["parts"][0]["text"]
                logger.info(f"Summarization successful for mode {mode}.")
                return summary.strip()
            else:
                logger.error(
                    f"Unexpected Gemini API response structure: {response_data}"
                )
                raise GeminiAPIException(
                    "Failed to parse summary from Gemini API response."
                )

        except httpx.HTTPStatusError as e:
            logger.error(
                f"Gemini API request failed: {e.response.status_code} - {e.response.text}"
            )
            raise GeminiAPIException(
                f"Gemini API error: {e.response.status_code} - {e.response.text}",
                status_code=e.response.status_code,
            ) from e
        except httpx.RequestError as e:
            logger.error(f"HTTPX request error to Gemini API: {e}")
            raise GeminiAPIException(
                f"Network error communicating with Gemini API: {e}"
            ) from e
        except Exception as e:
            logger.exception(
                f"An unexpected error occurred during summarization with mode {mode}: {e}"
            )
            raise SummarizationFailedException(
                f"Unexpected error in summarization: {e}"
            ) from e

    async def close(self):
        await self.http_client.aclose()
