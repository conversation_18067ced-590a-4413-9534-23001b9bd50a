import hashlib
import re
from io import String<PERSON>
from urllib.parse import urlparse, parse_qs
from typing import Optional
import xml.etree.ElementTree as ET


def extract_video_id(youtube_url: str) -> Optional[str]:
    """Extracts video_id from various YouTube URL formats."""
    parsed_url = urlparse(str(youtube_url))  # pydantic HttpUrl to str
    if parsed_url.hostname in ("www.youtube.com", "youtube.com", "m.youtube.com"):
        if parsed_url.path == "/watch":
            query_params = parse_qs(parsed_url.query)
            return query_params.get("v", [None])[0]
        if parsed_url.path.startswith("/embed/"):
            return parsed_url.path.split("/embed/")[1].split("?")[0]
        if parsed_url.path.startswith("/v/"):
            return parsed_url.path.split("/v/")[1].split("?")[0]
    elif parsed_url.hostname == "youtu.be":
        return parsed_url.path[1:].split("?")[0]
    return None


def generate_summary_task_id(text: str, mode: str) -> str:
    """Generates a unique task ID for summarization."""
    return hashlib.sha256(text.encode("utf-8") + mode.encode("utf-8")).hexdigest()


def generate_playlist_task_id(playlist_url: str) -> str:
    """Generates a unique task ID for playlist processing."""
    return hashlib.sha256(str(playlist_url).encode("utf-8")).hexdigest()


def parse_ttml_to_text(ttml_content: str) -> str:
    """Parses TTML content and extracts plain text."""
    try:
        root = ET.fromstring(ttml_content)
        # TTML namespace can vary, find it or use a wildcard.
        # Common namespaces:
        # xmlns="http://www.w3.org/ns/ttml"
        # xmlns:tts="http://www.w3.org/ns/ttml#styling"
        # xmlns:ttm="http://www.w3.org/ns/ttml#metadata"

        # More robust approach for namespaces:
        namespaces = dict(
            [
                node
                for _, node in ET.iterparse(StringIO(ttml_content), events=["start-ns"])
            ]
        )
        if not namespaces:  # If no namespace is defined in the root
            namespaces = {"": "http://www.w3.org/ns/ttml"}

        lines = []
        # Iterate over 'p' (paragraph) elements within 'body' -> 'div'
        for p_element in root.findall(
            ".//{{{ns}}}p".format(ns=namespaces.get("", namespaces.get("tt"))),
            namespaces=namespaces,
        ):
            line_text = "".join(p_element.itertext()).strip()
            if line_text:
                lines.append(line_text)
        return "\n".join(lines)
    except ET.ParseError as e:
        # loguru.error(f"Failed to parse TTML: {e}") # logging should be in service
        raise ValueError(f"Invalid TTML format: {e}") from e
