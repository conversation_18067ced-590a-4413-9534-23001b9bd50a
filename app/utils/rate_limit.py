from aiolimiter import AsyncLimiter
from app.config import settings

# Shared limiter for all worker processing to achieve the global rate limit.
# Rate is tasks per second. So, 10 tasks/min = 10/60 tasks/sec.
# Max 1 burst means it strictly adheres to the rate over time.
worker_global_rate_limiter = AsyncLimiter(settings.WORKER_PROCESSING_RATE_LIMIT / 60, 1)

# Можно также создать отдельные лимитеры для типов API запросов, если нужно более гранулярно,
# но RateLimitConfig в main.py уже покрывает это для всех API.
