from sqlalchemy import Column, Integer, String, Text, DateTime, func, Enum as DBEnum
from app.database.engine import Base
from app.DTOs.api_models import (
    TaskStatusEnum as AppTaskStatusEnum,
)  # Чтобы не было конфликта имен


class TaskStatusLog(Base):  # Общая таблица для статусов всех задач
    __tablename__ = "task_status_log"

    id: Column[int] = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_id: Column[str] = Column(
        String, index=True, nullable=False
    )  # video_id, hash(text+mode), hash(playlist_url)
    task_type: Column[str] = Column(
        String, nullable=False
    )  # 'subtitle', 'summary', 'playlist'
    status: Column[DBEnum(AppTaskStatusEnum)] = Column(
        DBEnum(AppTaskStatusEnum), default=AppTaskStatusEnum.PENDING
    )

    # Для кешированных результатов
    video_id_ref: Column[str] = Column(
        String, nullable=True
    )  # FK на SubtitlesCache.video_id
    summary_id_ref: Column[str] = Column(
        String, nullable=True
    )  # FK на SummarizationCache.task_id
    playlist_id_ref: Column[str] = Column(
        String, nullable=True
    )  # FK на PlaylistCache.task_id

    error_message: Column[Text] = Column(Text, nullable=True)
    created_at: Column[DateTime] = Column(DateTime, default=func.now())
    updated_at: Column[DateTime] = Column(
        DateTime, default=func.now(), onupdate=func.now()
    )


class SubtitlesCache(Base):
    __tablename__ = "subtitles_cache"

    video_id: Column[str] = Column(String, primary_key=True, index=True)
    title: Column[str] = Column(String, nullable=True)
    publish_date: Column[DateTime] = Column(
        DateTime, nullable=True
    )  # Хранить как DateTime
    original_language: Column[str] = Column(String, nullable=True)
    subtitles_ru: Column[Text] = Column(Text, nullable=True)
    subtitles_en: Column[Text] = Column(Text, nullable=True)
    processed_at: Column[DateTime] = Column(DateTime, default=func.now())


class SummarizationCache(Base):
    __tablename__ = "summarization_cache"

    task_id: Column[str] = Column(
        String, primary_key=True, index=True
    )  # hash(text+mode)
    original_text_hash: Column[str] = Column(String, index=True)
    mode: Column[str] = Column(String, index=True)
    summary_text: Column[Text] = Column(Text)
    processed_at: Column[DateTime] = Column(DateTime, default=func.now())


class PlaylistCache(Base):
    __tablename__ = "playlist_cache"

    task_id: Column[str] = Column(
        String, primary_key=True, index=True
    )  # hash(playlist_url)
    playlist_url_hash: Column[str] = Column(String, index=True)  # Для поиска по URL
    video_ids_json: Column[Text] = Column(Text)  # JSON list of video_ids
    processed_at: Column[DateTime] = Column(DateTime, default=func.now())
