import asyncio
from typing import Union, <PERSON>, <PERSON><PERSON>, Dict
from loguru import logger
from app.config import settings
from app.DTOs.task_models import (
    SubtitleTaskData,
    SummarizationTaskData,
    PlaylistTaskData,
)  # Define these Pydantic models
from app.services.notification import NotificationService
from app.DTOs.api_models import WebSocketUpdate, TaskStatusEnum  # For notification
from app.services.cache import CacheService  # For DB logging
from app.config import Settings
from typing import Optional, Union


# Task data models (could be in DTOs/task_models.py)
class BaseTask:
    task_id: str
    task_type: str  # 'subtitle', 'summary', 'playlist'


class SubtitleTaskDataInternal(BaseTask):
    task_type: str = "subtitle"
    youtube_url: str  # Keep original URL for yt-dlp
    video_id: str  # Already extracted, used as task_id


class SummarizationTaskDataInternal(BaseTask):
    task_type: str = "summary"
    text_to_summarize: str
    mode: str
    # task_id is hash(text+mode)


class PlaylistTaskDataInternal(BaseTask):
    task_type: str = "playlist"
    playlist_url: str  # Original URL
    # task_id is hash(playlist_url)


TaskType = Union[
    SubtitleTaskDataInternal, SummarizationTaskDataInternal, PlaylistTaskDataInternal
]


class TaskQueueManager:
    def __init__(
        self, notification_service: "NotificationService", settings: Settings
    ):  # Add settings
        # Separate queues for different types of workload as per TS (I/O vs CPU/API)
        self.io_bound_queue: asyncio.Queue[
            Union[SubtitleTaskDataInternal, PlaylistTaskDataInternal]
        ] = asyncio.Queue(maxsize=settings.MAX_QUEUE_SIZE // 2)
        self.cpu_bound_queue: asyncio.Queue[SummarizationTaskDataInternal] = (
            asyncio.Queue(maxsize=settings.MAX_QUEUE_SIZE // 2)
        )

        self.notification_service = notification_service
        self.task_statuses: Dict[str, WebSocketUpdate] = {}
        self._status_lock = asyncio.Lock()
        self.settings = settings  # Store settings

    def get_io_bound_queue(
        self,
    ) -> asyncio.Queue[Union[SubtitleTaskDataInternal, PlaylistTaskDataInternal]]:
        return self.io_bound_queue

    def get_cpu_bound_queue(self) -> asyncio.Queue[SummarizationTaskDataInternal]:
        return self.cpu_bound_queue

    async def update_task_status_and_log(
        self,
        task_id: str,
        task_type: str,
        status: TaskStatusEnum,
        cache_service: CacheService,  # Passed by worker for DB operations
        data: Any = None,
        error: Optional[str] = None,
        progress: Optional[float] = None,
        result_reference_id: Optional[str] = None,  # For TaskStatusLog
    ):
        """Updates in-memory status, sends WebSocket notification, and logs to DB."""
        async with self._status_lock:
            update = WebSocketUpdate(
                task_id=task_id,
                task_type=task_type,
                status=status,
                data=data,
                error=error,
                progress=progress,
            )
            self.task_statuses[task_id] = update

        await self.notification_service.broadcast_update(update)
        logger.debug(
            f"Task {task_id} ({task_type}) in-memory status updated to {status}. Notifying clients."
        )

        try:
            # Persist to TaskStatusLog in DB
            # Worker provides its own session for this via cache_service
            await cache_service.upsert_task_log_status(
                task_id=task_id,
                task_type=task_type,
                status=status,
                result_reference_id=result_reference_id,
                error_message=error,
                # session is handled by cache_service using its factory
            )
            logger.debug(f"Task {task_id} ({task_type}) status {status} logged to DB.")
        except Exception as db_err:
            logger.error(f"Failed to log task {task_id} status to DB: {db_err}")
            # Continue without DB log if it fails, but log the error. In-memory/WS update already sent.

    async def submit_task(
        self, task_data: TaskType, cache_service: CacheService
    ) -> Tuple[str, TaskStatusEnum, Optional[str]]:  # task_id, status, error_detail
        queue: asyncio.Queue
        initial_status = TaskStatusEnum.PENDING
        error_detail: Optional[str] = None

        if isinstance(task_data, (SubtitleTaskDataInternal, PlaylistTaskDataInternal)):
            queue = self.io_bound_queue
        elif isinstance(task_data, SummarizationTaskDataInternal):
            queue = self.cpu_bound_queue
        else:
            logger.error(f"Unknown task type for submission: {type(task_data)}")
            # This case should ideally be caught by Pydantic validation or earlier logic
            return task_data.task_id, TaskStatusEnum.FAILED, "Unknown task type"

        try:
            queue.put_nowait(task_data)
            logger.info(
                f"Task {task_data.task_id} ({task_data.task_type}) submitted to queue."
            )
        except asyncio.QueueFull:
            logger.warning(
                f"Queue for {task_data.task_type} is full. Task {task_data.task_id} rejected."
            )
            initial_status = TaskStatusEnum.FAILED
            error_detail = "Service busy: task queue is full."

        # Update status (in-memory, WS, DB log)
        await self.update_task_status_and_log(
            task_id=task_data.task_id,
            task_type=task_data.task_type,
            status=initial_status,
            cache_service=cache_service,  # For DB logging of PENDING/FAILED_QUEUE_FULL
            error=error_detail,
            result_reference_id=task_data.video_id
            if isinstance(task_data, SubtitleTaskDataInternal)
            and initial_status != TaskStatusEnum.FAILED
            else task_data.task_id
            if initial_status != TaskStatusEnum.FAILED
            else None,
        )
        return task_data.task_id, initial_status, error_detail

    async def get_task_status(self, task_id: str) -> Optional[WebSocketUpdate]:
        async with self._status_lock:
            return self.task_statuses.get(task_id)

    # Getter methods for queues for workers
    def get_subtitle_queue(
        self,
    ) -> asyncio.Queue[Union[SubtitleTaskDataInternal, PlaylistTaskDataInternal]]:
        return self.subtitle_queue  # type: ignore

    def get_ai_queue(self) -> asyncio.Queue[SummarizationTaskDataInternal]:
        return self.ai_queue
