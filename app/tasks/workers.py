import asyncio
from typing import List, Coroutine, Any
from sqlalchemy.ext.asyncio import async_sessionmaker, AsyncSession
from loguru import logger
from typing import Optional

from app.config import Settings
from app.DTOs.task_models import (
    SubtitleTaskDataInternal,
    SummarizationTaskDataInternal,
    PlaylistTaskDataInternal,
    TaskDataUnion,
)
from app.DTOs.api_models import TaskStatusEnum, SubtitleData, SummaryData, PlaylistData
from app.services.youtube import YouTubeService
from app.services.ai import AIService
from app.services.cache import CacheService
from app.services.notification import NotificationService
from app.tasks.manager import TaskQueueManager  # For queue types
from app.exceptions import (
    VideoNotFoundException,
    SubtitlesNotAvailableException,
    SummarizationFailedException,
    PlaylistProcessingFailedException,
    AppException,
)
from app.utils.rate_limit import worker_global_rate_limiter  # Shared global limiter

WORKER_TASKS_GLOBAL_LIST: List[asyncio.Task] = []


async def process_subtitle_task(
    task_data: SubtitleTaskDataInternal,
    youtube_service: YouTubeService,
    cache_service: CacheService,
    session: AsyncSession,
) -> SubtitleData:
    logger.info(f"Processing subtitle task: {task_data.task_id}")
    sub_data = await youtube_service.get_subtitles_and_metadata(
        str(task_data.youtube_url), task_data.video_id
    )

    if not sub_data:
        raise VideoNotFoundException(
            f"Could not retrieve metadata or subtitles for {task_data.video_id}"
        )
    if not sub_data.subtitles_ru and not sub_data.subtitles_en:
        # Store metadata even if subtitles are not found, but mark as failure for subtitles part
        # await cache_service.store_subtitles(sub_data, session) # Optionally cache metadata
        raise SubtitlesNotAvailableException(
            f"No Russian or English subtitles for {task_data.video_id}"
        )

    await cache_service.store_subtitles(sub_data, session)
    # The task_log status will be updated by the main worker loop after this returns
    # The reference ID for subtitles is the video_id itself
    await cache_service.upsert_task_log_status(
        task_id=task_data.task_id,
        task_type=task_data.task_type,
        status=TaskStatusEnum.COMPLETED,  # Temporary status, will be confirmed by manager
        result_reference_id=task_data.video_id,  # video_id is the key for SubtitlesCache
        session=session,
    )
    return sub_data


async def process_playlist_task(
    task_data: PlaylistTaskDataInternal,
    youtube_service: YouTubeService,
    cache_service: CacheService,
    session: AsyncSession,
) -> PlaylistData:
    logger.info(
        f"Processing playlist task: {task_data.task_id} for URL {task_data.playlist_url}"
    )
    video_ids = await youtube_service.get_playlist_video_ids(
        str(task_data.playlist_url)
    )

    if video_ids is None:  # Could be empty list if playlist is empty, None if error
        raise PlaylistProcessingFailedException(
            f"Failed to extract video IDs for playlist {task_data.playlist_url}"
        )

    playlist_result_data = PlaylistData(video_ids=video_ids)
    await cache_service.store_playlist(
        task_data.task_id, str(task_data.playlist_url), video_ids, session
    )
    await cache_service.upsert_task_log_status(
        task_id=task_data.task_id,
        task_type=task_data.task_type,
        status=TaskStatusEnum.COMPLETED,  # Temporary
        result_reference_id=task_data.task_id,  # task_id is the key for PlaylistCache
        session=session,
    )
    return playlist_result_data


async def process_summary_task(
    task_data: SummarizationTaskDataInternal,
    ai_service: AIService,
    cache_service: CacheService,
    session: AsyncSession,
) -> SummaryData:
    logger.info(
        f"Processing summary task: {task_data.task_id} for mode {task_data.mode}"
    )
    summary_text = await ai_service.summarize_text(
        task_data.text_to_summarize, task_data.mode
    )

    if not summary_text:  # Should be handled by AIService raising an exception
        raise SummarizationFailedException("AI service returned an empty summary.")

    summary_result_data = SummaryData(summary=summary_text)
    await cache_service.store_summary(
        task_id=task_data.task_id,
        original_text=task_data.text_to_summarize,
        mode=task_data.mode,
        summary_text=summary_text,
        session=session,
    )
    await cache_service.upsert_task_log_status(
        task_id=task_data.task_id,
        task_type=task_data.task_type,
        status=TaskStatusEnum.COMPLETED,  # Temporary
        result_reference_id=task_data.task_id,  # task_id is the key for SummarizationCache
        session=session,
    )
    return summary_result_data


async def worker_loop(
    worker_id: int,
    queue: asyncio.Queue[TaskDataUnion],
    task_processor_map: dict,  # Maps task_type to processing function
    task_queue_manager: TaskQueueManager,
    cache_service: CacheService,  # This instance uses db_session_factory
    notification_service: NotificationService,
    # Specific services needed by processors
    youtube_service: YouTubeService,
    ai_service: AIService,
    settings: Settings,
):
    logger.info(f"Worker {worker_id} starting for queue: {queue!r}")
    while True:
        try:
            task_data = await queue.get()
            logger.info(
                f"Worker {worker_id} picked up task: {task_data.task_id} ({task_data.task_type})"
            )

            await (
                worker_global_rate_limiter.acquire()
            )  # Adhere to global processing rate

            # Update status to PROCESSING (in-memory and DB)
            await task_queue_manager.update_task_status_and_log(
                task_id=task_data.task_id,
                task_type=task_data.task_type,
                status=TaskStatusEnum.PROCESSING,
                cache_service=cache_service,  # Pass cache_service for DB write
            )

            result_data: Any = None
            error_message: Optional[str] = None
            final_status: TaskStatusEnum = TaskStatusEnum.COMPLETED
            result_reference_id: Optional[str] = (
                task_data.task_id
            )  # Default for summary/playlist

            async with (
                cache_service._db_session_factory() as session
            ):  # Get a new session for this task
                try:
                    processor_func = task_processor_map[task_data.task_type]

                    # Pass relevant services to the specific processor
                    if task_data.task_type == "subtitle":
                        result_data = await processor_func(
                            task_data, youtube_service, cache_service, session
                        )
                        result_reference_id = (
                            task_data.video_id
                        )  # Specific for subtitles
                    elif task_data.task_type == "playlist":
                        result_data = await processor_func(
                            task_data, youtube_service, cache_service, session
                        )
                    elif task_data.task_type == "summary":
                        result_data = await processor_func(
                            task_data, ai_service, cache_service, session
                        )

                    logger.info(
                        f"Task {task_data.task_id} processed successfully by worker {worker_id}."
                    )

                except AppException as e:  # Catch specific app exceptions first
                    logger.warning(
                        f"AppException in worker {worker_id} for task {task_data.task_id}: {e.detail}"
                    )
                    error_message = e.detail
                    final_status = TaskStatusEnum.FAILED
                except Exception as e:
                    logger.exception(
                        f"Unexpected error in worker {worker_id} for task {task_data.task_id}: {e}"
                    )
                    error_message = (
                        "An unexpected error occurred during task processing."
                    )
                    final_status = TaskStatusEnum.FAILED

                # Final status update (in-memory and DB)
                await task_queue_manager.update_task_status_and_log(
                    task_id=task_data.task_id,
                    task_type=task_data.task_type,
                    status=final_status,
                    data=result_data
                    if final_status == TaskStatusEnum.COMPLETED
                    else None,
                    error=error_message,
                    cache_service=cache_service,  # Pass cache_service for DB write
                    result_reference_id=result_reference_id
                    if final_status == TaskStatusEnum.COMPLETED
                    else None,
                )

            queue.task_done()

        except asyncio.CancelledError:
            logger.info(
                f"Worker {worker_id} received cancellation request. Shutting down..."
            )
            break
        except Exception as e:
            # Log errors in the worker's main loop itself (e.g., queue.get() issues)
            logger.exception(f"Critical error in worker {worker_id} loop: {e}")
            await asyncio.sleep(5)  # Avoid fast spinning on persistent error


def run_workers(
    task_queue_manager: TaskQueueManager,
    youtube_service: YouTubeService,
    ai_service: AIService,
    cache_service: CacheService,  # Instance with db_session_factory
    notification_service: NotificationService,
    settings: Settings,
) -> List[asyncio.Task]:
    processor_map = {
        "subtitle": process_subtitle_task,
        "playlist": process_playlist_task,
        "summary": process_summary_task,
    }

    # For simplicity, all workers can pull from any queue if we merge queues,
    # or we can have specialized workers.
    # The TS mentioned 2 workers: subtitles + summarization.
    # Let's assume Subtitle worker handles both subtitle and playlist tasks (I/O bound with yt-dlp)
    # And AI worker handles summarization tasks (API/CPU bound)

    for i in range(settings.NUM_SUBTITLE_WORKERS):
        task = asyncio.create_task(
            worker_loop(
                worker_id=f"SubtitleWorker-{i + 1}",
                queue=task_queue_manager.get_io_bound_queue(),  # Combined queue for subs/playlists
                task_processor_map=processor_map,
                task_queue_manager=task_queue_manager,
                cache_service=cache_service,
                notification_service=notification_service,
                youtube_service=youtube_service,
                ai_service=ai_service,  # Though not directly used by sub/playlist, map needs it
                settings=settings,
            )
        )
        WORKER_TASKS_GLOBAL_LIST.append(task)

    for i in range(settings.NUM_AI_WORKERS):
        task = asyncio.create_task(
            worker_loop(
                worker_id=f"AIWorker-{i + 1}",
                queue=task_queue_manager.get_cpu_bound_queue(),  # Queue for summarization
                task_processor_map=processor_map,
                task_queue_manager=task_queue_manager,
                cache_service=cache_service,
                notification_service=notification_service,
                youtube_service=youtube_service,  # Though not directly used by AI, map needs it
                ai_service=ai_service,
                settings=settings,
            )
        )
        WORKER_TASKS_GLOBAL_LIST.append(task)

    logger.info(f"Launched {len(WORKER_TASKS_GLOBAL_LIST)} workers.")
    return WORKER_TASKS_GLOBAL_LIST


async def stop_workers():
    if not WORKER_TASKS_GLOBAL_LIST:
        logger.info("No active workers to stop.")
        return

    logger.info(f"Attempting to stop {len(WORKER_TASKS_GLOBAL_LIST)} workers...")
    for task in WORKER_TASKS_GLOBAL_LIST:
        if not task.done():
            task.cancel()

    results = await asyncio.gather(*WORKER_TASKS_GLOBAL_LIST, return_exceptions=True)
    WORKER_TASKS_GLOBAL_LIST.clear()  # Clear the list after stopping

    for i, result in enumerate(results):
        if isinstance(result, asyncio.CancelledError):
            logger.info(f"Worker task {i + 1} successfully cancelled.")
        elif isinstance(result, Exception):
            logger.error(
                f"Worker task {i + 1} raised an exception during shutdown: {result}"
            )
        else:
            logger.info(f"Worker task {i + 1} finished gracefully.")
    logger.info("All worker stop requests processed.")
