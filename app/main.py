from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import async_sessionmaker, AsyncSession
from litestar import Litestar, Router, get, Request
from litestar.exceptions import HTTPException
from litestar.status_codes import HTTP_500_INTERNAL_SERVER_ERROR
from litestar.di import Provide
from litestar.middleware.rate_limit import RateLimitConfig
from loguru import logger
import sys

from app.config import settings
from app.database.engine import create_tables, get_db_session, AsyncSessionFactory
from app.services.youtube import YouTubeService
from app.services.ai import AIService
from app.services.cache import CacheService
from app.services.notification import NotificationService
from app.tasks.manager import TaskQueueManager
from app.tasks.workers import run_workers, stop_workers  # To be implemented
from app.controllers.subtitle_controller import SubtitleController
from app.controllers.summary_controller import SummaryController
from app.controllers.playlist_controller import PlaylistController
from app.controllers.websocket_controller import (
    WebSocketTaskController,
)  # To be implemented
from app.exceptions import AppException  # Custom base exception

# --- Logging Setup ---
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL.upper(),
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
)
logger.add(
    "logs/app.log",
    rotation="10 MB",
    retention="7 days",
    level="DEBUG",  # File logging
    format="{time} {level} {message}",
)


# --- Rate Limiting ---
# Note: Litestar's built-in RateLimitConfig is in-memory.
# For distributed systems, an external store (Redis) would be needed.
rate_limit_config = RateLimitConfig(
    rate_limit=(settings.API_SUBMIT_RATE_LIMIT_PER_MINUTE, 60)
)


# --- Dependencies ---
async def provide_db_session(
    db_session_factory: async_sessionmaker = AsyncSessionFactory,
) -> AsyncSession:
    async with db_session_factory() as session:
        yield session


# Instantiate services and managers that need to be singletons
notification_service = NotificationService()
task_queue_manager = TaskQueueManager(notification_service=notification_service)
youtube_service = YouTubeService()
ai_service = AIService(settings=settings)
cache_service = CacheService()


dependencies = {
    "db_session": Provide(get_db_session, sync_to_thread=False),
    "settings": Provide(lambda: settings, sync_to_thread=False),
    "youtube_service": Provide(lambda: youtube_service, sync_to_thread=False),
    "ai_service": Provide(lambda: ai_service, sync_to_thread=False),
    "cache_service": Provide(lambda: cache_service, sync_to_thread=False),
    "task_queue_manager": Provide(lambda: task_queue_manager, sync_to_thread=False),
    "notification_service": Provide(lambda: notification_service, sync_to_thread=False),
}


# --- Exception Handling ---
def generic_exception_handler(request: Request, exc: Exception) -> HTTPException:
    logger.exception(f"Unhandled exception during request to {request.url}: {exc}")
    # Hide internal details in production
    detail = (
        str(exc)
        if settings.PYTHON_ENV == "development"
        else "An internal server error occurred."
    )

    if isinstance(exc, AppException):  # Custom exceptions can define their status code
        return HTTPException(detail=detail, status_code=exc.status_code)

    return HTTPException(detail=detail, status_code=HTTP_500_INTERNAL_SERVER_ERROR)


# --- Lifecycle Hooks ---
@asynccontextmanager
async def lifespan(app: Litestar):
    logger.info("Application startup: Initializing database and starting workers...")
    await create_tables()

    # Start background workers
    # Pass necessary dependencies to worker runners
    worker_tasks = run_workers(
        task_queue_manager=task_queue_manager,
        youtube_service=youtube_service,
        ai_service=ai_service,
        cache_service=CacheService(
            db_session_factory=AsyncSessionFactory
        ),  # Workers need their own session factory
        notification_service=notification_service,
        settings=settings,
    )
    logger.info("Workers started.")

    yield  # Application runs here

    logger.info("Application shutdown: Stopping workers...")
    await stop_workers(worker_tasks)
    logger.info("Workers stopped.")
    logger.info("Application shutdown complete.")


# --- Router & App ---
api_v1_router = Router(
    path="/api/v1",
    route_handlers=[SubtitleController, SummaryController, PlaylistController],
    # middleware=[rate_limit_config.middleware] # Apply rate limit to all /api/v1 routes
)

websocket_router = Router(
    path="/ws",
    route_handlers=[
        WebSocketTaskController
    ],  # This controller will use NotificationService
)


@get("/")
async def health_check() -> dict[str, str]:
    return {"status": "ok", "message": "Welcome to YouTube Summarizer API!"}


app = Litestar(
    route_handlers=[health_check, api_v1_router, websocket_router],
    dependencies=dependencies,
    exception_handlers={
        Exception: generic_exception_handler,  # Catch-all
        HTTPException: lambda _, exc: exc,  # Litestar's own HTTPExceptions re-raised
    },
    lifespan=lifespan,
    debug=settings.PYTHON_ENV == "development",
    # openapi_config can be added here for Swagger/OpenAPI docs
)
