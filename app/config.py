from typing import Dict, Any, List
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import HttpUrl, Field


class SummarizationMode(BaseSettings):
    model_name: str = "gemini-1.5-flash-latest"
    temperature: float = 0.7
    prompt_template: str = "Summarize the following text concisely: {text}"


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    PYTHON_ENV: str = "development"
    LOG_LEVEL: str = "INFO"

    DATABASE_URL: str = "sqlite+aiosqlite:///./youtube_summarizer.db"
    # Example for PostgreSQL:
    # DATABASE_URL: str = "postgresql+psycopg://user:pass@host:port/dbname"

    GEMINI_API_KEY: str = Field(..., validation_alias="GEMINI_API_KEY")

    # Режимы суммаризации
    # В .env это можно задать как:
    # SUMMARIZATION_MODES__DEFAULT__MODEL_NAME='gemini-1.0-pro'
    # SUMMARIZATION_MODES__DETAILED__PROMPT_TEMPLATE='Provide a detailed summary of: {text}'
    SUMMARIZATION_MODES: Dict[str, SummarizationMode] = {
        "default": SummarizationMode(),
        "detailed": SummarizationMode(
            temperature=0.5,
            prompt_template="Provide a detailed and structured summary of the following text, highlighting key points and arguments:\n\n{text}",
        ),
    }

    # Очередь и воркеры
    MAX_QUEUE_SIZE: int = 1000
    NUM_SUBTITLE_WORKERS: int = 1  # Для I/O bound задач (yt-dlp)
    NUM_AI_WORKERS: int = 1  # Для CPU/API-bound задач (Gemini)
    WORKER_PROCESSING_RATE_LIMIT: int = 10  # Задач в минуту суммарно для всех воркеров

    # Rate Limiting API
    API_SUBMIT_RATE_LIMIT_PER_MINUTE: int = 50

    # WebSocket
    WS_UPDATE_CHANNEL_CAPACITY: int = (
        100  # Размер буфера для канала обновлений WebSocket
    )


settings = Settings()

# Пример доступа: settings.GEMINI_API_KEY, settings.SUMMARIZATION_MODES["default"].prompt_template
