[project]
name = "yt-subs-api9-litestar"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiolimiter>=1.2.1",
    "aiosqlite>=0.21.0",
    "google-generativeai>=0.8.5",
    "httpx>=0.28.1",
    "litestar>=2.16.0",
    "loguru>=0.7.3",
    "psycopg>=3.2.9",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "python-dotenv>=1.1.0",
    "sqlalchemy[asyncio]>=2.0.41",
    "yt-dlp>=2025.5.22",
]
